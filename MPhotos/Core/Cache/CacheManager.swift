//
//  CacheManager.swift
//  MPhotos
//
//  Created by Augment Agent on 2025-08-02.
//

import Foundation
import UIKit
import Photos

/// 缓存管理器 - 实现双层缓存架构
final class CacheManager: NSObject, CacheManaging {
    
    // MARK: - 单例模式
    static let shared = CacheManager()
    
    // MARK: - 核心组件
    private let memoryCache = NSCache<NSString, UIImage>()
    private let imageManager = PHCachingImageManager()
    private let queue = DispatchQueue(label: "cacheManager", qos: .userInitiated, attributes: .concurrent)
    
    // MARK: - 统计信息
    private var _hitCount: Int = 0
    private var _missCount: Int = 0
    private let statisticsQueue = DispatchQueue(label: "cacheStatistics", qos: .utility)
    
    // MARK: - 初始化
    private override init() {
        super.init()
        setupMemoryCache()
        setupImageManager()
        setupMemoryWarningObserver()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 私有设置方法
    
    private func setupMemoryCache() {
        memoryCache.totalCostLimit = 50 * 1024 * 1024  // 50MB
        memoryCache.countLimit = 200                    // 200张图片
        memoryCache.delegate = self                     // 监听清理事件
    }
    
    private func setupImageManager() {
        imageManager.allowsCachingHighQualityImages = true
    }
    
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(didReceiveMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func didReceiveMemoryWarning() {
        print("⚠️ 内存警告，清理缓存")
        Task {
            await clearMemoryCache()
        }
    }
    
    private func clearMemoryCache() async {
        await withCheckedContinuation { continuation in
            queue.async(flags: .barrier) { [weak self] in
                self?.memoryCache.removeAllObjects()
                print("✅ 已清理内存缓存")
                continuation.resume()
            }
        }
    }
    
    private func calculateImageCost(_ image: UIImage) -> Int {
        let pixelCount = Int(image.size.width * image.size.height * image.scale * image.scale)
        return pixelCount * 4 // 4 bytes per pixel (RGBA)
    }
    
    private func createImageRequestOptions() -> PHImageRequestOptions {
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.resizeMode = .exact
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false
        return options
    }
}

// MARK: - CacheManaging Implementation

extension CacheManager {
    
    // MARK: - 图片缓存
    
    func store(_ image: UIImage, for key: String) async {
        await withCheckedContinuation { continuation in
            queue.async(flags: .barrier) { [weak self] in
                let cost = self?.calculateImageCost(image) ?? 0
                self?.memoryCache.setObject(image, forKey: key as NSString, cost: cost)
                continuation.resume()
            }
        }
    }
    
    func image(for key: String) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            queue.async { [weak self] in
                let image = self?.memoryCache.object(forKey: key as NSString)
                continuation.resume(returning: image)
            }
        }
    }
    
    func removeImage(for key: String) async {
        await withCheckedContinuation { continuation in
            queue.async(flags: .barrier) { [weak self] in
                self?.memoryCache.removeObject(forKey: key as NSString)
                continuation.resume()
            }
        }
    }
    
    func clearAll() async {
        await clearMemoryCache()
        imageManager.stopCachingImagesForAllAssets()
    }
    
    // MARK: - PHAsset图片请求
    
    func requestImage(for asset: PHAsset, targetSize: CGSize, contentMode: PHImageContentMode = .aspectFill) async -> UIImage? {
        let key = cacheKey(for: asset, size: targetSize, contentMode: contentMode)
        
        // 检查内存缓存
        if let cachedImage = await image(for: key) {
            statisticsQueue.async { [weak self] in
                self?._hitCount += 1
            }
            return cachedImage
        }
        
        // 缓存未命中，从PHImageManager请求
        statisticsQueue.async { [weak self] in
            self?._missCount += 1
        }
        
        return await withCheckedContinuation { continuation in
            let options = createImageRequestOptions()
            imageManager.requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: contentMode,
                options: options
            ) { [weak self] image, info in
                guard let image = image else {
                    continuation.resume(returning: nil)
                    return
                }
                
                // 检查是否为最终高质量图片
                let isDegraded = info?[PHImageResultIsDegradedKey] as? Bool ?? false
                if !isDegraded {
                    // 存储到内存缓存
                    Task { @MainActor [weak self] in
                        await self?.store(image, for: key)
                    }
                }
                
                continuation.resume(returning: image)
            }
        }
    }

    /// 🔑 关键方法：回调式请求图片（解决内存泄露的核心实现）
    @discardableResult
    func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
        let key = cacheKey(for: asset, size: targetSize, contentMode: .aspectFill)

        // 🔑 关键：同步检查内存缓存
        if let cachedImage = memoryCache.object(forKey: key as NSString) {
            statisticsQueue.async { [weak self] in
                self?._hitCount += 1
            }
            print("🎯 缓存命中: \(key)")

            DispatchQueue.main.async {
                completion(cachedImage)
            }
            return PHInvalidImageRequestID // 🔑 关键：表示缓存命中，无需请求
        }

        // 🔑 关键：直接进行PHImageManager请求，返回真实requestID
        statisticsQueue.async { [weak self] in
            self?._missCount += 1
        }

        let options = createImageRequestOptions()
        return imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            guard let image = image else {
                DispatchQueue.main.async { completion(nil) }
                return
            }

            // 检查是否为最终高质量图片
            let isDegraded = info?[PHImageResultIsDegradedKey] as? Bool ?? false
            if !isDegraded {
                // 存储到内存缓存
                let cost = self?.calculateImageCost(image) ?? 0
                self?.memoryCache.setObject(image, forKey: key as NSString, cost: cost)
                print("💾 图片已缓存: \(key)")
            }

            DispatchQueue.main.async {
                completion(image)
            }
        }
    }

    // MARK: - 预加载

    func preloadAssets(_ assets: [PHAsset], targetSize: CGSize) {
        guard !assets.isEmpty else { return }

        let options = createImageRequestOptions()
        options.deliveryMode = .fastFormat // 预加载使用快速模式

        imageManager.startCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        )

        print("🚀 开始预加载 \(assets.count) 个资源")
    }

    func stopPreloading(_ assets: [PHAsset], targetSize: CGSize) {
        imageManager.stopCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
    }

    func stopAllPreloading() {
        imageManager.stopCachingImagesForAllAssets()
    }

    // MARK: - 缓存信息

    var currentMemoryUsage: Int {
        return memoryCache.totalCostLimit
    }

    var cacheCount: Int {
        return memoryCache.countLimit
    }

    var hitRate: Double {
        return statisticsQueue.sync {
            let total = _hitCount + _missCount
            guard total > 0 else { return 0.0 }
            return Double(_hitCount) / Double(total)
        }
    }

    // MARK: - 缓存配置

    func setCacheLimit(_ limit: Int) {
        memoryCache.totalCostLimit = limit
    }

    func setCountLimit(_ limit: Int) {
        memoryCache.countLimit = limit
    }
}

// MARK: - NSCacheDelegate

extension CacheManager: NSCacheDelegate {
    func cache(_ cache: NSCache<AnyObject, AnyObject>, willEvictObject obj: AnyObject) {
        print("🗑️ 缓存清理对象")
    }
}
