//
//  LightweightCacheManager.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit
import Photos

/// 轻量级二级缓存管理器 - 简化版本，专注于性能和内存管理
final class LightweightCacheManager: CacheManaging {
    
    // MARK: - 单例
    static let shared = LightweightCacheManager()
    
    // MARK: - 私有属性
    
    /// 内存缓存（一级缓存）
    private let memoryCache = NSCache<NSString, UIImage>()
    
    /// 磁盘缓存队列
    private let diskQueue = DispatchQueue(label: "com.mphotos.cache.disk", qos: .utility)
    
    /// 磁盘缓存目录
    private let diskCacheDirectory: URL
    
    /// 内存监控器
    private let memoryMonitor = MemoryMonitor()
    
    /// 缓存统计
    private var hitCount: Int = 0
    private var missCount: Int = 0
    
    // MARK: - 配置常量
    
    private struct Config {
        static let maxMemoryCost: Int = 100 * 1024 * 1024 // 100MB
        static let maxDiskSize: Int = 500 * 1024 * 1024   // 500MB
        static let maxCacheAge: TimeInterval = 7 * 24 * 3600 // 7天
        static let compressionQuality: CGFloat = 0.8
    }
    
    // MARK: - 初始化
    
    private init() {
        // 设置内存缓存
        memoryCache.totalCostLimit = Config.maxMemoryCost
        memoryCache.countLimit = 200 // 最多缓存200张图片
        
        // 创建磁盘缓存目录
        let cacheDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        diskCacheDirectory = cacheDir.appendingPathComponent("MPhotos/Images")
        
        try? FileManager.default.createDirectory(at: diskCacheDirectory, 
                                               withIntermediateDirectories: true)
        
        // 设置内存监控
        setupMemoryMonitoring()
        
        // 启动时清理过期缓存
        cleanupExpiredCache()
    }
    
    // MARK: - 公共接口
    
    /// 获取图片（优先从内存缓存，然后磁盘缓存）
    func image(for key: String) async -> UIImage? {
        // 1. 检查内存缓存
        if let image = memoryCache.object(forKey: key as NSString) {
            hitCount += 1
            return image
        }

        // 2. 检查磁盘缓存
        if let image = loadImageFromDisk(key: key) {
            // 加载到内存缓存
            let cost = calculateImageCost(image)
            memoryCache.setObject(image, forKey: key as NSString, cost: cost)
            hitCount += 1
            return image
        }

        missCount += 1
        return nil
    }
    
    /// 存储图片到缓存
    func store(_ image: UIImage, for key: String) async {
        // 1. 存储到内存缓存
        let cost = calculateImageCost(image)
        memoryCache.setObject(image, forKey: key as NSString, cost: cost)

        // 2. 异步存储到磁盘缓存
        diskQueue.async { [weak self] in
            self?.saveImageToDisk(image, key: key)
        }
    }
    
    /// 移除指定缓存
    func removeImage(for key: String) async {
        memoryCache.removeObject(forKey: key as NSString)

        diskQueue.async { [weak self] in
            self?.removeImageFromDisk(key: key)
        }
    }

    /// 清空所有缓存
    func clearAll() async {
        memoryCache.removeAllObjects()

        diskQueue.async { [weak self] in
            self?.clearDiskCache()
        }
    }
    
    /// 清空内存缓存（内存警告时调用）
    func clearMemoryCache() {
        memoryCache.removeAllObjects()
        print("🧹 内存缓存已清空")
    }
    
    /// 获取缓存统计信息
    func getCacheStatistics() -> CacheStatistics {
        let memoryUsage = getCurrentMemoryUsage()
        let diskUsage = getDiskCacheSize()
        
        return CacheStatistics(
            hitCount: hitCount,
            missCount: missCount,
            hitRate: hitCount > 0 ? Double(hitCount) / Double(hitCount + missCount) : 0,
            memoryUsage: memoryUsage,
            diskUsage: diskUsage
        )
    }
    
    // MARK: - 私有方法
    
    /// 从磁盘加载图片
    private func loadImageFromDisk(key: String) -> UIImage? {
        let fileURL = diskCacheDirectory.appendingPathComponent(key)
        
        guard FileManager.default.fileExists(atPath: fileURL.path),
              let data = try? Data(contentsOf: fileURL),
              let image = UIImage(data: data) else {
            return nil
        }
        
        // 更新文件访问时间
        try? FileManager.default.setAttributes([.modificationDate: Date()], 
                                             ofItemAtPath: fileURL.path)
        
        return image
    }
    
    /// 保存图片到磁盘
    private func saveImageToDisk(_ image: UIImage, key: String) {
        guard let data = image.jpegData(compressionQuality: Config.compressionQuality) else {
            return
        }
        
        let fileURL = diskCacheDirectory.appendingPathComponent(key)
        
        do {
            try data.write(to: fileURL)
        } catch {
            print("💾 磁盘缓存写入失败: \(error)")
        }
    }
    
    /// 从磁盘移除图片
    private func removeImageFromDisk(key: String) {
        let fileURL = diskCacheDirectory.appendingPathComponent(key)
        try? FileManager.default.removeItem(at: fileURL)
    }
    
    /// 清空磁盘缓存
    private func clearDiskCache() {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: diskCacheDirectory, 
                                                                  includingPropertiesForKeys: nil)
            for file in files {
                try FileManager.default.removeItem(at: file)
            }
        } catch {
            print("💾 磁盘缓存清理失败: \(error)")
        }
    }
    
    /// 清理过期缓存
    private func cleanupExpiredCache() {
        diskQueue.async { [weak self] in
            self?.performDiskCleanup()
        }
    }
    
    /// 执行磁盘清理
    private func performDiskCleanup() {
        do {
            let files = try FileManager.default.contentsOfDirectory(
                at: diskCacheDirectory,
                includingPropertiesForKeys: [URLResourceKey.contentModificationDateKey, URLResourceKey.fileSizeKey]
            )

            let now = Date()
            var totalSize: Int64 = 0
            var filesToDelete: [URL] = []

            for file in files {
                let attributes = try file.resourceValues(forKeys: [URLResourceKey.contentModificationDateKey, URLResourceKey.fileSizeKey])

                if let modificationDate = attributes.contentModificationDate,
                   now.timeIntervalSince(modificationDate) > Config.maxCacheAge {
                    filesToDelete.append(file)
                } else if let fileSize = attributes.fileSize {
                    totalSize += Int64(fileSize)
                }
            }

            // 删除过期文件
            for file in filesToDelete {
                try? FileManager.default.removeItem(at: file)
            }

            // 如果总大小超限，删除最旧的文件
            if totalSize > Config.maxDiskSize {
                let sortedFiles = files.sorted { file1, file2 in
                    let date1 = (try? file1.resourceValues(forKeys: [URLResourceKey.contentModificationDateKey]))?.contentModificationDate ?? Date.distantPast
                    let date2 = (try? file2.resourceValues(forKeys: [URLResourceKey.contentModificationDateKey]))?.contentModificationDate ?? Date.distantPast
                    return date1 < date2
                }

                for file in sortedFiles {
                    if totalSize <= Config.maxDiskSize { break }

                    if let fileSize = (try? file.resourceValues(forKeys: [URLResourceKey.fileSizeKey]))?.fileSize {
                        totalSize -= Int64(fileSize)
                        try? FileManager.default.removeItem(at: file)
                    }
                }
            }
            
        } catch {
            print("🧹 磁盘清理失败: \(error)")
        }
    }
    
    /// 计算图片内存占用
    private func calculateImageCost(_ image: UIImage) -> Int {
        let pixelCount = Int(image.size.width * image.size.height * image.scale * image.scale)
        return pixelCount * 4 // RGBA，每像素4字节
    }
    
    /// 获取当前内存使用量
    private func getCurrentMemoryUsage() -> Int {
        return memoryMonitor.getCurrentMemoryUsage()
    }
    
    /// 获取磁盘缓存大小
    private func getDiskCacheSize() -> Int64 {
        do {
            let files = try FileManager.default.contentsOfDirectory(
                at: diskCacheDirectory,
                includingPropertiesForKeys: [.fileSizeKey]
            )
            
            return files.reduce(0) { total, file in
                let fileSize = (try? file.resourceValues(forKeys: [.fileSizeKey]))?.fileSize ?? 0
                return total + Int64(fileSize)
            }
        } catch {
            return 0
        }
    }
    
    /// 设置内存监控
    private func setupMemoryMonitoring() {
        memoryMonitor.onMemoryWarning = { [weak self] in
            self?.clearMemoryCache()
        }
    }
}

// MARK: - CacheManaging 协议实现

extension LightweightCacheManager {

    // MARK: - PHAsset 图片请求

    /// 异步请求图片
    func requestImage(for asset: PHAsset, targetSize: CGSize, contentMode: PHImageContentMode = .aspectFill) async -> UIImage? {
        let key = cacheKey(for: asset, size: targetSize, contentMode: contentMode)

        // 检查缓存
        if let cachedImage = await image(for: key) {
            return cachedImage
        }

        // 从 PHImageManager 请求
        return await withCheckedContinuation { continuation in
            let options = PHImageRequestOptions()
            options.deliveryMode = .highQualityFormat
            options.isNetworkAccessAllowed = true

            PHImageManager.default().requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: contentMode,
                options: options
            ) { [weak self] image, info in
                guard let image = image else {
                    continuation.resume(returning: nil)
                    return
                }

                // 缓存图片
                Task {
                    await self?.store(image, for: key)
                }

                continuation.resume(returning: image)
            }
        }
    }

    /// 回调式请求图片
    @discardableResult
    func requestImage(for asset: PHAsset, targetSize: CGSize, completion: @escaping (UIImage?) -> Void) -> PHImageRequestID {
        let key = cacheKey(for: asset, size: targetSize, contentMode: .aspectFill)

        // 同步检查内存缓存
        if let cachedImage = memoryCache.object(forKey: key as NSString) {
            hitCount += 1
            DispatchQueue.main.async {
                completion(cachedImage)
            }
            return PHInvalidImageRequestID
        }

        // 从 PHImageManager 请求
        missCount += 1
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true

        return PHImageManager.default().requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            guard let image = image else {
                DispatchQueue.main.async { completion(nil) }
                return
            }

            // 缓存图片
            let cost = self?.calculateImageCost(image) ?? 0
            self?.memoryCache.setObject(image, forKey: key as NSString, cost: cost)

            DispatchQueue.main.async {
                completion(image)
            }
        }
    }

    // MARK: - 预加载

    /// 预加载资源
    func preloadAssets(_ assets: [PHAsset], targetSize: CGSize) {
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat

        PHImageManager.default().startCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        )
    }

    /// 停止预加载指定资源
    func stopPreloading(_ assets: [PHAsset], targetSize: CGSize) {
        PHImageManager.default().stopCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: nil
        )
    }

    /// 停止所有预加载
    func stopAllPreloading() {
        PHImageManager.default().stopCachingImagesForAllAssets()
    }

    // MARK: - 缓存信息属性

    /// 当前内存使用量
    var currentMemoryUsage: Int {
        return getCurrentMemoryUsage()
    }

    /// 当前缓存项数量
    var cacheCount: Int {
        return memoryCache.countLimit
    }

    /// 缓存命中率
    var hitRate: Double {
        let total = hitCount + missCount
        guard total > 0 else { return 0.0 }
        return Double(hitCount) / Double(total)
    }

    // MARK: - 缓存配置

    /// 设置缓存大小限制
    func setCacheLimit(_ limit: Int) {
        memoryCache.totalCostLimit = limit
    }

    /// 设置缓存数量限制
    func setCountLimit(_ limit: Int) {
        memoryCache.countLimit = limit
    }
}

// MARK: - 缓存统计数据结构

struct CacheStatistics {
    let hitCount: Int
    let missCount: Int
    let hitRate: Double
    let memoryUsage: Int
    let diskUsage: Int64
    
    var formattedHitRate: String {
        return String(format: "%.1f%%", hitRate * 100)
    }
    
    var formattedMemoryUsage: String {
        return ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory)
    }
    
    var formattedDiskUsage: String {
        return ByteCountFormatter.string(fromByteCount: diskUsage, countStyle: .file)
    }
}
