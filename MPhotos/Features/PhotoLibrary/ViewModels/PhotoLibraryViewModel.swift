//
//  PhotoLibraryViewModel.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import Foundation
import Photos

/// 图库视图模型 - 管理照片数据和业务逻辑
class PhotoLibraryViewModel: BaseViewModel {
    
    // MARK: - 回调闭包
    
    /// 照片数据更新回调
    var onPhotosUpdated: (([PhotoModel]) -> Void)?
    
    /// 错误处理回调
    var onError: ((Error) -> Void)?
    
    /// 设置变更回调
    var onSettingsChanged: ((LibrarySettings) -> Void)?
    
    // MARK: - 属性
    
    /// 照片库服务
    private let photoLibraryService = SimplifiedPhotoLibraryService.shared

    /// 设置服务
    private let settingsService = SettingsService.shared
    
    /// 当前照片数组
    private var photos: [PhotoModel] = []
    
    /// 是否正在进行手势缩放（防止手势期间重新加载数据）
    private var isGestureActive: Bool = false
    
    /// 手势结束后是否需要重新加载数据
    private var shouldReloadAfterGesture: Bool = false
    
    /// 当前设置
    var currentSettings: LibrarySettings {
        return settingsService.librarySettings
    }
    
    /// 当前照片数量
    var photoCount: Int {
        return photos.count
    }
    
    /// 是否有照片数据
    var hasPhotos: Bool {
        return !photos.isEmpty
    }
    
    /// 检查是否正在进行手势操作
    var isGestureInProgress: Bool {
        return isGestureActive
    }
    
    // MARK: - 初始化
    
    /// 使用默认服务初始化
    override init() {
        super.init()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        cleanup()
        print("🗑️ PhotoLibraryViewModel 已释放")
    }
    
    // MARK: - 数据绑定设置
    
    override func setupBindings() {
        super.setupBindings()
        
        // 监听设置变更
        settingsService.addDelegate(self)
        
        // 监听后台照片加载完成通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePhotosUpdated),
            name: NSNotification.Name("PhotosUpdated"),
            object: nil
        )
        
        print("✅ PhotoLibraryViewModel 数据绑定设置完成")
    }
    
    /// 处理后台照片更新
    @objc private func handlePhotosUpdated() {
        // 🔧 关键修复：手势期间完全忽略后台照片更新，避免重置布局
        if isGestureActive {
            print("🤏 手势进行中，忽略后台照片更新，避免干扰布局变更")
            shouldReloadAfterGesture = true  // 标记手势结束后需要重新加载
            return
        }
        
        let oldCount = self.photos.count
        let newPhotos = photoLibraryService.cachedPhotos
        
        print("📷 后台照片更新检查：当前 \(oldCount) 张，新数据 \(newPhotos.count) 张，手势状态：\(isGestureActive ? "活跃" : "非活跃")")
        
        // 防止重复更新：只有当照片数量增加时才更新
        if newPhotos.count > oldCount {
            // 🔧 修复：移除过于严格的增量限制，允许大批量后台加载
            let increment = newPhotos.count - oldCount

            // 只检查是否是合理的增加（不是减少）
            if increment > 0 {
                self.photos = newPhotos

                // 🔧 三重检查：更新UI前最后确认手势状态和时间
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }

                    if !self.isGestureActive {
                        // 通知UI增量更新
                        self.onPhotosUpdated?(self.photos)
                        print("🔄 后台照片加载完成，从 \(oldCount) 张增加到 \(newPhotos.count) 张（增量：\(increment)）")
                    } else {
                        print("🤏 准备更新UI时发现手势仍然活跃，跳过UI更新")
                    }
                }
            } else {
                print("⚠️ 照片数量减少：从 \(oldCount) 到 \(newPhotos.count)，跳过更新")
            }
        } else if newPhotos.count == oldCount && oldCount == 0 {
            // 🔧 修复：处理首次加载的情况
            self.photos = newPhotos

            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }

                if !self.isGestureActive {
                    self.onPhotosUpdated?(self.photos)
                    print("🔄 首次照片加载完成，共 \(newPhotos.count) 张")
                }
            }
        }
    }
    
    // MARK: - 数据加载
    
    /// 加载照片数据
    override func loadData() {
        loadPhotos()
    }
    
    /// 加载照片数据
    func loadPhotos() {
        setLoading()
        
        let settings = currentSettings
        // print("📸 开始加载照片，当前设置: 布局=\(settings.layoutType.displayName), 排序=\(settings.sortOrder.displayName)")
        
        photoLibraryService.fetchPhotos(with: settings) { [weak self] photos in
            guard let self = self else { return }
            
            self.photos = photos
            self.setLoaded()
            
            // 通知UI更新
            self.onPhotosUpdated?(photos)
            
                            // print("✅ 照片加载完成，共 \(photos.count) 张照片")
        }
    }
    
    /// 刷新照片数据
    func refreshPhotos() {
        // print("🔄 刷新照片数据")
        loadPhotos()
    }
    
    // MARK: - 照片访问
    
    /// 获取指定索引的照片
    /// - Parameter index: 照片索引
    /// - Returns: PhotoModel或nil
    func getPhoto(at index: Int) -> PhotoModel? {
        guard index >= 0 && index < photos.count else {
            print("⚠️ 照片索引越界: \(index), 总数: \(photos.count)")
            return nil
        }
        return photos[index]
    }
    
    /// 获取所有照片
    /// - Returns: 照片数组
    func getAllPhotos() -> [PhotoModel] {
        return photos
    }
    
    /// 根据ID查找照片
    /// - Parameter id: 照片ID
    /// - Returns: PhotoModel或nil
    func findPhoto(by id: String) -> PhotoModel? {
        return photos.first { $0.id == id }
    }
    
    // MARK: - 照片操作
    
    /// 切换照片的选中状态
    /// - Parameter index: 照片索引
    /// - Returns: 是否成功切换
    @discardableResult
    func togglePhotoSelection(at index: Int) -> Bool {
        guard index >= 0 && index < photos.count else { return false }
        
        photos[index].isSelected.toggle()
        print("✅ 照片选中状态切换: \(photos[index].id) -> \(photos[index].isSelected)")
        return true
    }
    
    /// 获取当前选中的照片
    /// - Returns: 选中的照片数组
    func getSelectedPhotos() -> [PhotoModel] {
        return photos.filter { $0.isSelected }
    }
    
    /// 清除所有选中状态
    func clearAllSelections() {
        photos.indices.forEach { photos[$0].isSelected = false }
        print("🔄 清除所有照片选中状态")
    }
    
    /// 全选照片
    func selectAllPhotos() {
        photos.indices.forEach { photos[$0].isSelected = true }
        print("✅ 全选照片，共 \(photos.count) 张")
    }
    
    // MARK: - 设置管理
    
    /// 设置手势活跃状态
    /// - Parameter active: 是否活跃
    func setGestureActive(_ active: Bool) {
        let oldState = isGestureActive
        isGestureActive = active
        
        if active {
            print("🤏 手势缩放开始 - 暂停数据刷新和后台处理")
            
            // 🚀 超级优化：手势期间完全停止后台照片处理
            // 移除后台照片更新监听，避免冲突
            NotificationCenter.default.removeObserver(
                self,
                name: NSNotification.Name("PhotosUpdated"),
                object: nil
            )
            
        } else {
            print("🤏 手势缩放结束 - 恢复数据刷新和后台处理")
            
            // 🔧 关键修复：延迟重新启用后台处理，确保手势完全结束
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
                guard let self = self else { return }
                
                // 🚀 超级优化：手势结束后重新启用后台照片处理
                // 重新添加后台照片更新监听
                NotificationCenter.default.addObserver(
                    self,
                    selector: #selector(self.handlePhotosUpdated),
                    name: NSNotification.Name("PhotosUpdated"),
                    object: nil
                )
                
                // 🔧 修复：手势结束后恢复后台加载，而不是重新加载数据
                if self.shouldReloadAfterGesture {
                    self.shouldReloadAfterGesture = false
                    print("🤏 手势结束后恢复后台照片加载")
                    // 恢复后台加载任务，而不是重新开始
                    self.photoLibraryService.resumeBackgroundLoadingIfNeeded()
                }
            }
        }
        
        // 只有状态真正变化时才打印日志
        if oldState != active {
            print("🤏 手势状态变化: \(oldState ? "活跃" : "非活跃") → \(active ? "活跃" : "非活跃")")
        }
    }
    
    /// 更新布局类型
    /// - Parameter layoutType: 新的布局类型
    func updateLayoutType(_ layoutType: LibrarySettings.LayoutType) {
        settingsService.updateLayoutType(layoutType)
        print("🎛️ 布局类型更新为: \(layoutType.displayName)")
    }
    
    /// 静默更新布局类型（手势专用 - 不触发数据重新加载）
    /// - Parameter layoutType: 新的布局类型
    func updateLayoutTypeQuietly(_ layoutType: LibrarySettings.LayoutType) {
        settingsService.updateLayoutTypeQuietly(layoutType)
        print("🤏 静默更新布局类型: \(layoutType.displayName)")
    }
    
    /// 更新排序方式
    /// - Parameter sortOrder: 新的排序方式
    func updateSortOrder(_ sortOrder: LibrarySettings.SortOrder) {
        settingsService.updateSortOrder(sortOrder)
        print("📊 排序方式更新为: \(sortOrder.displayName)")
    }

    /// 切换日期分组
    func toggleDateGrouping() {
        settingsService.updateDateGrouping(!currentSettings.groupByDate)
        print("📅 日期分组切换为: \(currentSettings.groupByDate ? "开启" : "关闭")")
    }

    /// 更新选中的相册
    /// - Parameter albumIdentifiers: 相册标识符集合
    func updateSelectedAlbums(_ albumIdentifiers: Set<String>) {
        settingsService.updateSelectedAlbums(albumIdentifiers)
        print("📂 已选择相册数量: \(albumIdentifiers.count)")
    }
    
    // MARK: - 缓存管理
    
    /// 开始缓存指定范围的照片
    /// - Parameters:
    ///   - range: 照片索引范围
    ///   - targetSize: 目标尺寸
    func startCaching(for range: Range<Int>, targetSize: CGSize) {
        let photosToCache = Array(photos[range])
        photoLibraryService.startCaching(for: photosToCache, targetSize: targetSize)
    }
    
    /// 停止缓存指定范围的照片
    /// - Parameters:
    ///   - range: 照片索引范围
    ///   - targetSize: 目标尺寸
    func stopCaching(for range: Range<Int>, targetSize: CGSize) {
        let photosToStopCaching = Array(photos[range])
        photoLibraryService.stopCaching(for: photosToStopCaching, targetSize: targetSize)
    }
    
    /// 停止所有缓存
    func stopAllCaching() {
        photoLibraryService.stopAllCaching()
        print("🛑 停止所有照片缓存")
    }
    
    // MARK: - 统计信息
    
    /// 获取照片统计信息
    /// - Returns: 统计信息字符串
    func getPhotoStatistics() -> String {
        let totalCount = photos.count
        let selectedCount = getSelectedPhotos().count
        let videoCount = photos.filter { $0.isVideo }.count
        let livePhotoCount = photos.filter { $0.isLivePhoto }.count
        
        var stats = ["=== 照片统计信息 ==="]
        stats.append("总照片数: \(totalCount)")
        stats.append("已选择: \(selectedCount)")
        stats.append("视频数: \(videoCount)")
        stats.append("Live Photo数: \(livePhotoCount)")
        stats.append("当前布局: \(currentSettings.layoutType.displayName)")
        stats.append("排序方式: \(currentSettings.sortOrder.displayName)")
        stats.append("日期分组: \(currentSettings.groupByDate ? "开启" : "关闭")")

        return stats.joined(separator: "\n")
    }
    
    // MARK: - 搜索功能
    
    /// 搜索照片
    /// - Parameter searchText: 搜索文本
    /// - Returns: 搜索结果
    func searchPhotos(with searchText: String) -> [PhotoModel] {
        guard !searchText.isEmpty else { return photos }
        
        let lowercasedSearchText = searchText.lowercased()
        
        return photos.filter { photo in
            // 这里可以根据需要添加更多搜索条件
            // 比如按日期、标签、位置等搜索
            if let creationDate = photo.creationDate {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy年MM月dd日"
                let dateString = formatter.string(from: creationDate)
                if dateString.lowercased().contains(lowercasedSearchText) {
                    return true
                }
            }
            
            // 按文件类型搜索
            if photo.isVideo && "视频".contains(lowercasedSearchText) {
                return true
            }
            
            if photo.isLivePhoto && "live photo".lowercased().contains(lowercasedSearchText) {
                return true
            }
            
            return false
        }
    }
    
    // MARK: - 内存管理
    
    /// 清理资源（在deinit中调用）
    private func cleanup() {
        // 移除设置监听
        settingsService.removeDelegate(self)
        
        // 停止所有缓存
        stopAllCaching()
        
        print("🗑️ PhotoLibraryViewModel 资源清理完成")
    }
}

// MARK: - SettingsChangeDelegate

extension PhotoLibraryViewModel: SettingsChangeDelegate {
    
    func settingsDidChange(_ settings: LibrarySettings) {
        print("⚙️ 设置发生变更 - 新布局: \(settings.layoutType.displayName)")

        // 通知UI设置已变更
        onSettingsChanged?(settings)

        // 🚀 超级优化：手势期间完全忽略设置变更，避免冲突
        if isGestureActive {
            print("🤏 手势进行中，完全忽略设置变更以避免冲突")
            // 不设置shouldReloadAfterGesture，因为手势本身已经处理了布局变更
            return
        }

        // 🔧 关键修复：通过检查实际照片排序来判断是否需要重新加载
        let needsDataReload = shouldReloadDataForSettings(settings)

        if needsDataReload {
            print("📸 数据相关设置变更，重新加载照片数据")

            // 🔧 修复：取消之前的后台加载任务，避免冲突
            photoLibraryService.cancelBackgroundLoading()
            refreshPhotos()
        } else {
            print("🎨 仅UI相关设置变更，无需重新加载数据")
            print("   - 仅布局类型变更: \(settings.layoutType.displayName)")

            // 🔧 修复：布局切换时保持现有数据，不取消后台加载
            print("🔄 保持现有照片数据和后台加载任务")
        }
    }

    /// 判断是否需要重新加载数据
    /// - Parameter newSettings: 新的设置
    /// - Returns: 是否需要重新加载
    private func shouldReloadDataForSettings(_ newSettings: LibrarySettings) -> Bool {
        // 如果没有照片数据，肯定需要加载
        if photos.isEmpty {
            return true
        }

        // 检查当前照片的排序是否与新设置匹配
        let currentPhotosAreNewestFirst = isPhotosOrderedNewestFirst()
        let newSettingsWantNewestFirst = (newSettings.sortOrder == .newestFirst)

        // 如果排序方式不匹配，需要重新加载
        if currentPhotosAreNewestFirst != newSettingsWantNewestFirst {
            print("📊 排序方式变更：当前照片排序=\(currentPhotosAreNewestFirst ? "最新在前" : "最旧在前")，新设置=\(newSettingsWantNewestFirst ? "最新在前" : "最旧在前")")
            return true
        }

        return false
    }

    /// 检查当前照片数组是否按最新在前排序
    /// - Returns: true表示最新在前，false表示最旧在前
    private func isPhotosOrderedNewestFirst() -> Bool {
        guard photos.count >= 2 else { return true }

        // 比较前两张照片的创建时间
        let firstPhotoDate = photos[0].creationDate ?? Date.distantPast
        let secondPhotoDate = photos[1].creationDate ?? Date.distantPast

        return firstPhotoDate >= secondPhotoDate
    }
}

// MARK: - 错误处理

extension PhotoLibraryViewModel {
    
    /// 处理照片加载错误
    /// - Parameter error: 错误对象
    private func handlePhotoLoadError(_ error: Error) {
        setError(error.localizedDescription)
        onError?(error)
        print("❌ 照片加载错误: \(error.localizedDescription)")
    }
} 