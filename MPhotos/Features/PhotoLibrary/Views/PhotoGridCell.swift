//
//  PhotoGridCell.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit
import Photos

/// 照片网格单元格 - 用于展示照片缩略图和相关信息
class PhotoGridCell: UICollectionViewCell {
    
    // MARK: - 标识符
    static let identifier = "PhotoGridCell"
    
    // MARK: - UI组件
    
    /// 照片图像视图
    private let imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = .systemGray6
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()
    
    /// 视频时长标签
    private let durationLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.monospacedDigitSystemFont(ofSize: 12, weight: .medium)
        label.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        label.textAlignment = .center
        label.layer.cornerRadius = 4
        label.clipsToBounds = true
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    /// Live Photo指示器
    private let livePhotoIndicator: UIImageView = {
        let imageView = UIImageView()
        // 使用iOS 17兼容的系统图标，如果livephoto不可用则使用camera.circle
        if let liveIcon = UIImage(systemName: "livephoto") {
            imageView.image = liveIcon
        } else if let fallbackIcon = UIImage(systemName: "camera.circle") {
            imageView.image = fallbackIcon
        }
        imageView.tintColor = .white
        imageView.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        imageView.layer.cornerRadius = 4
        imageView.clipsToBounds = true
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()
    
    /// 选中指示器
    private let selectionIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.3)
        view.layer.borderColor = UIColor.systemBlue.cgColor
        view.layer.borderWidth = 3
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    /// 选中圆圈
    private let selectionCircle: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBlue
        view.layer.cornerRadius = 12
        view.layer.borderColor = UIColor.white.cgColor
        view.layer.borderWidth = 2
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    /// 选中勾号
    private let checkmarkIcon: UIImageView = {
        let imageView = UIImageView()
        if let checkmark = UIImage(systemName: "checkmark") {
            imageView.image = checkmark
        }
        imageView.tintColor = .white
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()
    
    // MARK: - 属性
    
    /// 当前照片模型
    private var currentPhoto: PhotoModel?
    
    /// 图像请求ID（用于取消请求）
    private var imageRequestID: PHImageRequestID?
    
    /// 是否在选择模式
    private var isSelectionMode: Bool = false
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupGestures()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
        setupGestures()
    }
    
    // MARK: - 生命周期
    
    override func prepareForReuse() {
        super.prepareForReuse()
        
        // 取消正在进行的图像请求
        cancelImageRequest()
        
        // 重置UI状态
        imageView.image = nil
        durationLabel.isHidden = true
        livePhotoIndicator.isHidden = true
        selectionIndicator.isHidden = true
        selectionCircle.isHidden = true
        currentPhoto = nil
        
        // 重置圆角
        layer.cornerRadius = 0
    }
    
    // MARK: - UI设置
    
    /// 设置UI组件
    private func setupUI() {
        backgroundColor = .systemGray6
        clipsToBounds = true
        
        // 添加子视图
        contentView.addSubview(imageView)
        contentView.addSubview(durationLabel)
        contentView.addSubview(livePhotoIndicator)
        contentView.addSubview(selectionIndicator)
        contentView.addSubview(selectionCircle)
        selectionCircle.addSubview(checkmarkIcon)
        
        // 初始状态隐藏指示器
        durationLabel.isHidden = true
        livePhotoIndicator.isHidden = true
        selectionIndicator.isHidden = true
        selectionCircle.isHidden = true
    }
    
    /// 设置约束
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 图像视图填满整个cell
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            // 视频时长标签在右下角
            durationLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -4),
            durationLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -4),
            durationLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 30),
            durationLabel.heightAnchor.constraint(equalToConstant: 16),
            
            // Live Photo指示器在左下角
            livePhotoIndicator.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 4),
            livePhotoIndicator.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -4),
            livePhotoIndicator.widthAnchor.constraint(equalToConstant: 20),
            livePhotoIndicator.heightAnchor.constraint(equalToConstant: 16),
            
            // 选中指示器覆盖整个cell
            selectionIndicator.topAnchor.constraint(equalTo: contentView.topAnchor),
            selectionIndicator.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            selectionIndicator.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            selectionIndicator.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            // 选中圆圈在右上角
            selectionCircle.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -4),
            selectionCircle.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
            selectionCircle.widthAnchor.constraint(equalToConstant: 24),
            selectionCircle.heightAnchor.constraint(equalToConstant: 24),
            
            // 勾号在圆圈中心
            checkmarkIcon.centerXAnchor.constraint(equalTo: selectionCircle.centerXAnchor),
            checkmarkIcon.centerYAnchor.constraint(equalTo: selectionCircle.centerYAnchor),
            checkmarkIcon.widthAnchor.constraint(equalToConstant: 12),
            checkmarkIcon.heightAnchor.constraint(equalToConstant: 12)
        ])
    }

    /// 设置手势
    private func setupGestures() {
        // 添加长按手势用于分析缩略图压缩比例
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
        longPress.minimumPressDuration = 1.0
        addGestureRecognizer(longPress)
    }

    /// 处理长按手势 - 分析缩略图压缩比例
    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        guard gesture.state == .began,
              let photo = currentPhoto,
              let thumbnailImage = imageView.image else { return }

        analyzeThumbnailCompressionRatio(photo: photo, thumbnailImage: thumbnailImage)
    }

    // MARK: - 配置方法
    
    /// 配置cell显示内容
    /// - Parameters:
    ///   - photo: 照片模型
    ///   - targetSize: 目标尺寸
    ///   - isSelectionMode: 是否在选择模式
    func configure(with photo: PhotoModel, targetSize: CGSize, isSelectionMode: Bool) {
        currentPhoto = photo
        self.isSelectionMode = isSelectionMode

        // 重置图片
        imageView.image = nil

        // 配置UI状态
        configureDurationLabel(for: photo)
        configureLivePhotoIndicator(for: photo)
        configureSelectionState(for: photo, isSelectionMode: isSelectionMode)
    }

    /// 设置图片
    func setImage(_ image: UIImage) {
        imageView.image = image
    }

    /// 设置占位图
    func setPlaceholderImage() {
        imageView.image = UIImage(systemName: "photo")
    }

    /// 配置cell显示内容（可选择是否自动加载图片）
    /// - Parameters:
    ///   - photo: 照片模型
    ///   - targetSize: 目标尺寸
    ///   - isSelectionMode: 是否在选择模式
    ///   - autoLoadImage: 是否自动加载图片
    func configure(with photo: PhotoModel, targetSize: CGSize, isSelectionMode: Bool, autoLoadImage: Bool) {
        currentPhoto = photo
        self.isSelectionMode = isSelectionMode

        // 移除圆角，保持原生iOS照片应用的方形样式
        layer.cornerRadius = 0
        imageView.layer.cornerRadius = 0

        // 可选择是否自动加载照片缩略图
        if autoLoadImage {
            loadThumbnail(for: photo, targetSize: targetSize)
        }

        // 配置视频时长标签
        configureDurationLabel(for: photo)

        // 配置Live Photo指示器
        configureLivePhotoIndicator(for: photo)

        // 配置选择状态
        configureSelectionState(for: photo, isSelectionMode: isSelectionMode)
    }
    
    /// 取消图片请求
    func cancelImageRequest() {
        if let requestID = imageRequestID {
            PHImageManager.default().cancelImageRequest(requestID)
            imageRequestID = nil
        }
    }
    
    /// 配置视频时长标签
    /// - Parameter photo: 照片模型
    private func configureDurationLabel(for photo: PhotoModel) {
        // 根据当前布局决定是否显示视频时长
        // 5列及以下显示，超过5列不显示
        let settings = SettingsService.shared.librarySettings
        let shouldShowDuration = settings.layoutType.columnsPerRow <= 5
        
        if photo.isVideo && photo.duration > 0 && shouldShowDuration {
            durationLabel.text = photo.formattedDuration()
            durationLabel.isHidden = false
        } else {
            durationLabel.isHidden = true
        }
    }
    
    /// 配置Live Photo指示器
    /// - Parameter photo: 照片模型
    private func configureLivePhotoIndicator(for photo: PhotoModel) {
        // 不显示Live Photo图标
        livePhotoIndicator.isHidden = true
    }
    
    /// 配置选择状态
    /// - Parameters:
    ///   - photo: 照片模型
    ///   - isSelectionMode: 是否在选择模式
    private func configureSelectionState(for photo: PhotoModel, isSelectionMode: Bool) {
        if isSelectionMode {
            selectionCircle.isHidden = false
            
            if photo.isSelected {
                selectionIndicator.isHidden = false
                selectionCircle.backgroundColor = .systemBlue
                checkmarkIcon.isHidden = false
            } else {
                selectionIndicator.isHidden = true
                selectionCircle.backgroundColor = UIColor.black.withAlphaComponent(0.3)
                checkmarkIcon.isHidden = true
            }
        } else {
            selectionIndicator.isHidden = true
            selectionCircle.isHidden = true
        }
    }
    
    // MARK: - 辅助方法
    
    /// 取消当前的图像请求（修复版本 - 解决内存泄露）
    private func cancelImageRequest() {
        // 🔑 关键：检查requestID有效性，正确取消PHImageManager请求
        if let requestID = imageRequestID, requestID != PHInvalidImageRequestID {
            PHImageManager.default().cancelImageRequest(requestID)
            print("🛑 已取消图片请求: \(requestID)")
        }

        // 同时取消PhotoLibraryService的请求（向后兼容）
        if let photo = currentPhoto {
            PhotoLibraryService.shared.cancelThumbnailRequest(for: photo.id)
        }

        imageRequestID = nil
    }
    
    /// 更新选择状态（用于动画）
    /// - Parameter isSelected: 是否选中
    func updateSelectionState(_ isSelected: Bool) {
        guard isSelectionMode else { return }
        
        UIView.animate(withDuration: 0.2) {
            if isSelected {
                self.selectionIndicator.isHidden = false
                self.selectionCircle.backgroundColor = .systemBlue
                self.checkmarkIcon.isHidden = false
            } else {
                self.selectionIndicator.isHidden = true
                self.selectionCircle.backgroundColor = UIColor.black.withAlphaComponent(0.3)
                self.checkmarkIcon.isHidden = true
            }
        }
    }

    /// 分析缩略图压缩比例
    private func analyzeThumbnailCompressionRatio(photo: PhotoModel, thumbnailImage: UIImage) {
        let originalSize = photo.pixelSize
        let thumbnailSize = thumbnailImage.size
        let cellSize = bounds.size

        // 计算压缩比例
        let originalAspectRatio = originalSize.width / originalSize.height
        let thumbnailAspectRatio = thumbnailSize.width / thumbnailSize.height
        let cellAspectRatio = cellSize.width / cellSize.height

        // 判断是否被压缩变形
        let aspectRatioDifference = abs(originalAspectRatio - thumbnailAspectRatio)
        let isCompressed = aspectRatioDifference > 0.01 // 1%的容差

        // 计算应该的裁剪尺寸（aspectFill模式）
        var expectedSize: CGSize
        if originalAspectRatio > cellAspectRatio {
            // 原图更宽，应该裁剪宽度
            expectedSize = CGSize(
                width: originalSize.height * cellAspectRatio,
                height: originalSize.height
            )
        } else {
            // 原图更高，应该裁剪高度
            expectedSize = CGSize(
                width: originalSize.width,
                height: originalSize.width / cellAspectRatio
            )
        }

        // 计算缩放比例
        let scaleX = thumbnailSize.width / originalSize.width
        let scaleY = thumbnailSize.height / originalSize.height
        let expectedScaleX = thumbnailSize.width / expectedSize.width
        let expectedScaleY = thumbnailSize.height / expectedSize.height

        print("""
        🔍 缩略图压缩分析报告
        ==================
        照片ID: \(photo.id)
        原始尺寸: \(Int(originalSize.width))x\(Int(originalSize.height))
        缩略图尺寸: \(Int(thumbnailSize.width))x\(Int(thumbnailSize.height))
        Cell尺寸: \(Int(cellSize.width))x\(Int(cellSize.height))
        期望尺寸: \(Int(expectedSize.width))x\(Int(expectedSize.height))

        宽高比分析:
        - 原始宽高比: \(String(format: "%.3f", originalAspectRatio))
        - 缩略图宽高比: \(String(format: "%.3f", thumbnailAspectRatio))
        - Cell宽高比: \(String(format: "%.3f", cellAspectRatio))
        - 差异: \(String(format: "%.3f", aspectRatioDifference))

        缩放比例:
        - X轴缩放: \(String(format: "%.3f", scaleX))
        - Y轴缩放: \(String(format: "%.3f", scaleY))
        - 期望X轴: \(String(format: "%.3f", expectedScaleX))
        - 期望Y轴: \(String(format: "%.3f", expectedScaleY))

        压缩状态: \(isCompressed ? "❌ 被压缩变形" : "✅ 正常裁剪")

        可能原因:
        \(isCompressed ? "- PHImageManager的resizeMode使用了.fast导致直接缩放\n- 图片格式特殊处理异常\n- 缓存层级处理不一致" : "- 正常的aspectFill裁剪\n- PHImageManager正确处理了宽高比")
        ==================
        """)
    }



    /// 获取当前显示的图片（供外部检测使用）
    func getCurrentImage() -> UIImage? {
        return imageView.image
    }

    /// 更新显示的图片（供外部修复使用）
    func updateImage(_ image: UIImage) {
        imageView.image = image
    }
}